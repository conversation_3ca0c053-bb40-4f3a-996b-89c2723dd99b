{"name": "resumate", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "concurrently \"vite\" \"nodemon server.js\"", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@tailwindcss/vite": "^4.1.5", "appwrite": "^18.1.1", "chart.js": "^4.4.9", "classnames": "^2.5.1", "concurrently": "^9.1.2", "cors": "^2.8.5", "dompurify": "^3.2.6", "dotenv": "^16.5.0", "express": "^5.1.0", "firebase": "^11.8.1", "framer-motion": "^12.9.4", "nodemon": "^3.1.10", "openai": "^4.103.0", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-router-dom": "^7.5.3", "react-to-pdf": "^2.0.0", "react-to-print": "^3.1.0", "tailwindcss": "^4.1.5"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.1"}}