// src/Components/ResumeUploadModal.jsx
import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  FaUpload, 
  FaTimes, 
  FaSpinner,
  FaCheckCircle,
  FaRobot,
  FaChartLine,
  FaBriefcase
} from 'react-icons/fa';
import toast from 'react-hot-toast';
import { useNavigate } from 'react-router-dom';

import { uploadFile, validateResumeFile } from '../services/fileStorage';
import { saveUploadedFile, createResume } from '../db/database';
import { parseResumeFromUpload, checkATSFromUpload } from '../utils/ai';
import { useResumeData } from '../Contexts/ResumeDataContext';

// Transform OpenAI parsed data to match the expected resume structure
const transformParsedDataToResumeFormat = (parsedData) => {
  return {
    name: parsedData.name || "",
    description: parsedData.description || "",
    contact: {
      email: parsedData.contact?.email || "",
      phone: parsedData.contact?.phone || "",
      location: parsedData.contact?.location || "",
      linkedin: parsedData.contact?.linkedin || "",
      github: parsedData.contact?.github || "",
      websiteURL: parsedData.contact?.website || "",
    },
    skills: parsedData.skills?.map(skill => ({
      domain: typeof skill === 'string' ? 'General' : skill.domain || 'General',
      languages: typeof skill === 'string' ? [skill] : skill.languages || [skill]
    })) || [{ domain: 'General', languages: [] }],
    experience: parsedData.experience?.map(exp => ({
      company: exp.company || "",
      role: exp.role || "",
      technologies: exp.technologies || "",
      years: exp.duration || "",
      description: exp.description || "",
    })) || [],
    projects: parsedData.projects?.map(proj => ({
      name: proj.name || "",
      description: proj.description || "",
      github: proj.link || "",
      demo: proj.demo || "",
    })) || [],
    education: {
      college: parsedData.education?.[0]?.institution || "",
      degree: parsedData.education?.[0]?.degree || "",
      specialization: parsedData.education?.[0]?.specialization || "",
      location: parsedData.education?.[0]?.location || "",
      startYear: parsedData.education?.[0]?.duration?.split('-')?.[0]?.trim() || "",
      endYear: parsedData.education?.[0]?.duration?.split('-')?.[1]?.trim() || "",
      cgpa: parsedData.education?.[0]?.gpa || parsedData.education?.[0]?.cgpa || "",
      school: "",
      tenth: "",
      twelfth: "",
    },
    achievements: parsedData.achievements?.map(ach => ({
      title: ach.title || "",
      description: ach.description || "",
      year: ach.year || "",
      month: ach.month || "",
    })) || [],
    certifications: parsedData.certifications || [],
  };
};

const ResumeUploadModal = ({ isOpen, onClose, onUploadSuccess }) => {
  const [uploadedFile, setUploadedFile] = useState(null);
  const [isUploading, setIsUploading] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const [selectedOptions, setSelectedOptions] = useState({
    createResume: false,
    checkATS: false,
    jobMatching: false
  });
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingStep, setProcessingStep] = useState('');
  
  const navigate = useNavigate();
  const { setResume } = useResumeData();

  // Handle file upload
  const handleFileUpload = async (files) => {
    const file = files[0];
    if (!file) return;

    // Validate file
    const validation = validateResumeFile(file);
    if (!validation.isValid) {
      validation.errors.forEach(error => toast.error(error));
      return;
    }

    setIsUploading(true);
    const uploadToastId = toast.loading('Uploading your resume...');
    
    try {
      // Upload to Appwrite
      const uploadResult = await uploadFile(file);
      
      // Save metadata to Firestore
      await saveUploadedFile(uploadResult);
      
      setUploadedFile(uploadResult);
      toast.success('Resume uploaded successfully!', { id: uploadToastId });
      
      // Notify parent component
      if (onUploadSuccess) {
        onUploadSuccess(uploadResult);
      }
    } catch (error) {
      console.error('Upload error:', error);
      
      let errorMessage = 'Failed to upload resume. Please try again.';
      if (error.message.includes('size')) {
        errorMessage = 'File is too large. Please upload a file smaller than 10MB.';
      } else if (error.message.includes('type')) {
        errorMessage = 'Invalid file type. Please upload PDF, DOC, DOCX, or TXT files.';
      }
      
      toast.error(errorMessage, { id: uploadToastId });
    } finally {
      setIsUploading(false);
    }
  };

  // Drag and drop handlers
  const handleDrag = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileUpload(e.dataTransfer.files);
    }
  }, []);

  // File input change handler
  const handleInputChange = (e) => {
    e.preventDefault();
    if (e.target.files && e.target.files[0]) {
      handleFileUpload(e.target.files);
    }
  };

  // Handle option selection
  const handleOptionToggle = (option) => {
    setSelectedOptions(prev => ({
      ...prev,
      [option]: !prev[option]
    }));
  };

  // Process selected options
  const handleDone = async () => {
    if (!uploadedFile) {
      toast.error('Please upload a resume first');
      return;
    }

    const selectedCount = Object.values(selectedOptions).filter(Boolean).length;
    if (selectedCount === 0) {
      toast.error('Please select at least one option');
      return;
    }

    setIsProcessing(true);

    try {
      // Process Create Resume
      if (selectedOptions.createResume) {
        setProcessingStep('Creating resume...');
        const parsedData = await parseResumeFromUpload(uploadedFile.fileUrl);
        const transformedData = transformParsedDataToResumeFormat(parsedData);
        await createResume(transformedData);
        toast.success('Resume created successfully!');
      }

      // Process ATS Check
      if (selectedOptions.checkATS) {
        setProcessingStep('Checking ATS compatibility...');
        const atsData = await checkATSFromUpload(uploadedFile.fileUrl);
        toast.success(`ATS Score: ${atsData.atsScore}/100`);
        
        // Navigate to ATS checker with results
        navigate('/ats-checker', { 
          state: { atsResult: atsData, uploadedFile } 
        });
      }

      // Process Job Matching
      if (selectedOptions.jobMatching) {
        setProcessingStep('Preparing job matching...');
        navigate('/job-fit-analyzer', { 
          state: { uploadedFile } 
        });
      }

      // Close modal after processing
      onClose();
      
    } catch (error) {
      console.error('Processing error:', error);
      toast.error('Failed to process resume. Please try again.');
    } finally {
      setIsProcessing(false);
      setProcessingStep('');
    }
  };

  // Reset modal state when closed
  const handleClose = () => {
    setUploadedFile(null);
    setSelectedOptions({
      createResume: false,
      checkATS: false,
      jobMatching: false
    });
    setIsProcessing(false);
    setProcessingStep('');
    onClose();
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={handleClose}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className="bg-gray-900 rounded-2xl p-8 max-w-md w-full mx-auto border border-gray-700"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-white">Upload Resume</h2>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-white transition-colors"
            >
              <FaTimes size={20} />
            </button>
          </div>

          {/* Upload Area */}
          <div
            className={`relative border-2 border-dashed rounded-xl p-6 text-center transition-all duration-300 mb-6 ${
              dragActive 
                ? 'border-blue-500 bg-blue-500/10' 
                : 'border-gray-600 hover:border-gray-500'
            } ${isUploading ? 'pointer-events-none opacity-60' : ''}`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            <input
              type="file"
              id="file-upload-modal"
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              onChange={handleInputChange}
              accept=".pdf,.doc,.docx,.txt"
              disabled={isUploading}
            />
            
            {isUploading ? (
              <div className="flex flex-col items-center">
                <FaSpinner className="animate-spin text-3xl text-blue-500 mb-3" />
                <p className="text-gray-300">Uploading...</p>
              </div>
            ) : uploadedFile ? (
              <div className="flex flex-col items-center">
                <FaCheckCircle className="text-3xl text-green-500 mb-3" />
                <p className="text-green-400 font-medium">{uploadedFile.fileName}</p>
                <p className="text-gray-400 text-sm">Ready to process</p>
              </div>
            ) : (
              <div className="flex flex-col items-center">
                <FaUpload className="text-3xl text-gray-400 mb-3" />
                <p className="text-gray-300 mb-2">upload your resume</p>
                <p className="text-gray-500 text-sm">PDF, DOC, DOCX, TXT (max 10MB)</p>
              </div>
            )}
          </div>

          {/* Options */}
          {uploadedFile && (
            <div className="space-y-3 mb-6">
              <OptionCheckbox
                icon={<FaUpload />}
                label="create resume"
                checked={selectedOptions.createResume}
                onChange={() => handleOptionToggle('createResume')}
              />
              <OptionCheckbox
                icon={<FaRobot />}
                label="check ATS score"
                checked={selectedOptions.checkATS}
                onChange={() => handleOptionToggle('checkATS')}
              />
              <OptionCheckbox
                icon={<FaBriefcase />}
                label="Job description matching"
                checked={selectedOptions.jobMatching}
                onChange={() => handleOptionToggle('jobMatching')}
              />
            </div>
          )}

          {/* Done Button */}
          {uploadedFile && (
            <button
              onClick={handleDone}
              disabled={isProcessing || Object.values(selectedOptions).every(v => !v)}
              className="w-full py-3 bg-white text-gray-900 rounded-xl font-medium hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
            >
              {isProcessing ? (
                <>
                  <FaSpinner className="animate-spin" />
                  {processingStep || 'Processing...'}
                </>
              ) : (
                'done'
              )}
            </button>
          )}
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

// Option Checkbox Component
const OptionCheckbox = ({ icon, label, checked, onChange }) => (
  <div
    onClick={onChange}
    className="flex items-center gap-3 p-3 border border-gray-600 rounded-xl cursor-pointer hover:border-gray-500 transition-colors"
  >
    <div className={`w-5 h-5 border-2 rounded ${checked ? 'bg-white border-white' : 'border-gray-500'} flex items-center justify-center`}>
      {checked && <div className="w-2 h-2 bg-gray-900 rounded-sm" />}
    </div>
    <div className="text-gray-400 text-lg">{icon}</div>
    <span className="text-gray-300 flex-1">{label}</span>
  </div>
);

export default ResumeUploadModal;
